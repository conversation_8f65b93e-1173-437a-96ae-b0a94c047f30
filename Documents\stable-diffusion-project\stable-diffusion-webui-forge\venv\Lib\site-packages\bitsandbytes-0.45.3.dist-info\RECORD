bitsandbytes-0.45.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bitsandbytes-0.45.3.dist-info/LICENSE,sha256=8IqUv7C9wdfxy1m62vD4xVGxDbhxGFYi2_H9A2r_qcs,1107
bitsandbytes-0.45.3.dist-info/METADATA,sha256=HZ2Or5gQqR9AnmFSa4FOgxPR_Zbkk-rJJlk5ZyTgSww,5059
bitsandbytes-0.45.3.dist-info/NOTICE.md,sha256=OpIf_XSa2lQICqX1wlMfagqddG9fsg_O4voC0qsvgSo,268
bitsandbytes-0.45.3.dist-info/RECORD,,
bitsandbytes-0.45.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes-0.45.3.dist-info/WHEEL,sha256=l0QJsP3XCd-HqqZ3bYeVoQOewj5RR7AcI-4xVCpFfoc,98
bitsandbytes-0.45.3.dist-info/top_level.txt,sha256=bK-Zzu-JyIIh4njm8jTYcbuqX-Z80XTcDal4lXCG0-M,13
bitsandbytes/__init__.py,sha256=ndQJcEijB8VfLNC-EKWd92uDssXV1tGkaPLQBsBzdsA,568
bitsandbytes/__main__.py,sha256=_ObgXmW-NG395jj_oP86gSOw2LgqMtrGBmpjZOiRcuY,94
bitsandbytes/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/__pycache__/__main__.cpython-312.pyc,,
bitsandbytes/__pycache__/cextension.cpython-312.pyc,,
bitsandbytes/__pycache__/consts.cpython-312.pyc,,
bitsandbytes/__pycache__/cuda_specs.cpython-312.pyc,,
bitsandbytes/__pycache__/functional.cpython-312.pyc,,
bitsandbytes/__pycache__/utils.cpython-312.pyc,,
bitsandbytes/autograd/__init__.py,sha256=OeUeF5-AE6e2Y99DYsWDb20dwvot38GY2xpJUHUrG3Q,68
bitsandbytes/autograd/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/autograd/__pycache__/_functions.cpython-312.pyc,,
bitsandbytes/autograd/_functions.py,sha256=H-nU0WcN2Nr-plJwkvGz9imymOh35jF2Nx90zFej8dA,20793
bitsandbytes/cextension.py,sha256=X1S0SdvxaMgz-S0AmajKflUDsHB2bLPrxlm8iTFKYzc,3767
bitsandbytes/consts.py,sha256=hhASlIvnZUQd4XXo1wlQ86KpSfVSUCW91F7-wVjfjlk,392
bitsandbytes/cuda_specs.py,sha256=8i3VHz7spxYpEnWj-mFNy5bSzAP7P0IL7WMhn2WPWLk,1233
bitsandbytes/diagnostics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/diagnostics/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/cuda.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/main.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/utils.cpython-312.pyc,,
bitsandbytes/diagnostics/cuda.py,sha256=dA2LtzC89miodjstIE8Pi3MHtFte2G05s6sxWhWfG88,7013
bitsandbytes/diagnostics/main.py,sha256=fAoGxpH-ctOihq6yB1y7-DRuxw9fZ_GRAnFsTfSDln8,2727
bitsandbytes/diagnostics/utils.py,sha256=eTZVw1AS-esmmpNj_NwCWX0nl_LP3aL-e83NjBiiulM,296
bitsandbytes/functional.py,sha256=-VCvQJWO8uOI55TwQmBZZLjoaTFOry1p6VGBlU5nx3k,110910
bitsandbytes/libbitsandbytes_cpu.dll,sha256=B0CBFVh4dSC7bCrt6qXrvW1AcgKRgfICbAsUKH9L86o,37376
bitsandbytes/libbitsandbytes_cuda117.dll,sha256=U1UrepDoewFTGwG9xFzZedgBpQfjPYBx3UCyt6s3gCI,20763648
bitsandbytes/libbitsandbytes_cuda118.dll,sha256=Njlyyr5yZE1_S2WHQ6ZxXm8SKZWKpiM7AP0aIvB967o,26360832
bitsandbytes/libbitsandbytes_cuda120.dll,sha256=nwedlAEvcoe6idF5X9OhOcg22gsxPlhdM-AIaNRtuLI,25628160
bitsandbytes/libbitsandbytes_cuda121.dll,sha256=ZmXhHQrLJNaTxOO-L25UoTIpHKfUiPzx0ZigBDXcV5E,25637888
bitsandbytes/libbitsandbytes_cuda122.dll,sha256=lMfa2A306jLPMQbbUwqvWzD5g8-sb6tKUYI1-2MCYhA,25645056
bitsandbytes/libbitsandbytes_cuda123.dll,sha256=QUxMsU75WNXPUT6d15q2bGW43xLL3Ij4KkzvaXxVGBE,25653248
bitsandbytes/libbitsandbytes_cuda124.dll,sha256=5YWTD2rbE9gpVORNcP-9NOuag_ohAii22h3B5SYq7H8,24887296
bitsandbytes/libbitsandbytes_cuda125.dll,sha256=EjjkCO-UbD4PwxD28tUL8OjTQAC0Yw0RTlDwUa--unk,24948224
bitsandbytes/libbitsandbytes_cuda126.dll,sha256=SQkvoxx39poZAAVbb_qC_c2lrL0o15DC59sVw8UsbJg,24973824
bitsandbytes/libbitsandbytes_cuda128.dll,sha256=JjfLjBDJnYkYrdbmVi5qfRskU7lLHv8MQghXotlo65A,23552512
bitsandbytes/nn/__init__.py,sha256=VpgW4WzMXZ2KO_1aNS5K7-0ZMoXQctU064hISBAzubU,623
bitsandbytes/nn/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/nn/__pycache__/modules.cpython-312.pyc,,
bitsandbytes/nn/__pycache__/triton_based_modules.cpython-312.pyc,,
bitsandbytes/nn/modules.py,sha256=VMgtB54OPaLHmnRlLFwNyCxmB3ocHq4d-LLCRpIpmv0,38255
bitsandbytes/nn/triton_based_modules.py,sha256=nHgsatAx6SDjF3T9WT0-gc1wyTpFkMxt5Ioxb-n6bno,10082
bitsandbytes/optim/__init__.py,sha256=AvB75Z18WaU0EfmE5tH73CwLQxyhLFznOc2VZ97Ovcw,903
bitsandbytes/optim/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adagrad.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adam.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adamw.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/ademamix.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lamb.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lars.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lion.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/optimizer.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/rmsprop.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/sgd.cpython-312.pyc,,
bitsandbytes/optim/adagrad.py,sha256=bHW6e66lQv9DBU2QVaTBY--3HAItkrvCZ-oi50bLyc0,8122
bitsandbytes/optim/adam.py,sha256=k6KvGjh9WI8qwUra_pJmcucIUTdxdyIzNzhiMK3BNmU,24469
bitsandbytes/optim/adamw.py,sha256=R9rNgRV1kaYiSw0rgFYRHYRTNE_Fa8kk10MfRyc8lBQ,14894
bitsandbytes/optim/ademamix.py,sha256=nHwVILNTefwbImwIbNyZi8d5jcTjLlnTWf_i-WksF18,13385
bitsandbytes/optim/lamb.py,sha256=PrxexS_YODc4r3ev5ii4AxTMPqmKAS6ESag0GvBXRds,8161
bitsandbytes/optim/lars.py,sha256=4rmMYBz_5CzlJZboT8XqLrt7s6qLu_54FWY7lyJn2gY,9667
bitsandbytes/optim/lion.py,sha256=dujq4h6nONJHpKAgWCYNJy8O8LUSeruJ7kLD8kFGF0Q,11931
bitsandbytes/optim/optimizer.py,sha256=ncCcPE-oR5B3ZPCl1_m2VaW2_VbJ2qCE-9E9FXnHrYc,31076
bitsandbytes/optim/rmsprop.py,sha256=ewxuwdPQMuL_gRSGEAAlKHrBMPpjR37BEyFFJbcDcbo,7982
bitsandbytes/optim/sgd.py,sha256=YIdIlQRbxWqP-HvV4dJ_pYXeTId1IrACefpc1z0IHgw,6637
bitsandbytes/research/__init__.py,sha256=jSLMzRcp8QOd1VbiQwcOFrQH0fl1pfBxt23c-GxxWmo,125
bitsandbytes/research/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/autograd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/research/autograd/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/autograd/__pycache__/_functions.cpython-312.pyc,,
bitsandbytes/research/autograd/_functions.py,sha256=agApMsEopZP-IvlYkJ7_IChN97FVXHc2kq8Wr-ZPgBI,14744
bitsandbytes/research/nn/__init__.py,sha256=_93TgkJ0CiQHJ3DL2VWoIqLE8urYCmXMNymVQiJSh4E,54
bitsandbytes/research/nn/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/nn/__pycache__/modules.cpython-312.pyc,,
bitsandbytes/research/nn/modules.py,sha256=13EgrXiYCuDbRewxTLvD7Bl-ltnkMN_8Tbm2Qx3MleA,2404
bitsandbytes/triton/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/triton/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/dequantize_rowwise.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_mixed_dequantize.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_rowwise_dequantize.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/matmul_perf_model.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_columnwise_and_transpose.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_global.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_rowwise.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/triton_utils.cpython-312.pyc,,
bitsandbytes/triton/dequantize_rowwise.py,sha256=YDVUsbRiRrmvH9yM-KqTbgfLt0VUounuOUiU2XlQB84,2104
bitsandbytes/triton/int8_matmul_mixed_dequantize.py,sha256=pfZna4Lg0nnbpFD2jPMHolobTcfCskcFf0FCTTkpiVM,8969
bitsandbytes/triton/int8_matmul_rowwise_dequantize.py,sha256=n38SCZ6Pt2GpHCJlUKjzKQEjJVuvUKLSTg1ORNW2LOE,8954
bitsandbytes/triton/matmul_perf_model.py,sha256=gaC6cu2m7PaGYl5CgCkEuCCZf9TL9J8MTAIdeKR6vxE,7431
bitsandbytes/triton/quantize_columnwise_and_transpose.py,sha256=jwbnRyjWRbD403CKxPOzF0JIzAmNcUandSq9AZaqqvw,2673
bitsandbytes/triton/quantize_global.py,sha256=L-nOgJoIv2sbiThisHBcHSzNiW4VkHlqXYdidE9TPws,4045
bitsandbytes/triton/quantize_rowwise.py,sha256=oaZ8xRSEFBc3bA-WrhHUwhQ7UIDnH6bEX0MzeeAbRJ4,2242
bitsandbytes/triton/triton_utils.py,sha256=95mMuH7SYNHz8EjcvWaKMN04FXbyOzN6_vD_YCRC17w,350
bitsandbytes/utils.py,sha256=k6QX_amGCJ4mCw74hjGLF8NZOV7f_QaysWUT-NA8e38,7032
